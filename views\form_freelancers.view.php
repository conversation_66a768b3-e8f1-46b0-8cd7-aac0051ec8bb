<?php
#region region DOCS
/** @var array $form_data */
/** @var array $form_errors */
/** @var string|null $form_success */
/** @var RangoExperiencia[] $rangos_experiencias */
/** @var NivelEducativo[] $niveles_educativos */
/** @var HusoHorario[] $husos_horarios */
/** @var Idioma[] $idiomas */
/** @var array $paises */

use App\classes\HusoHorario;
use App\classes\Idioma;
use App\classes\NivelEducativo;
use App\classes\RangoExperiencia;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<!-- ========== Meta Tags ========== -->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<!-- ======== Page title ============ -->
	<title><?php echo APP_NAME; ?> | Freelancers</title>
	
	<?php require_once __ROOT__ . '/views/head_section.view.php'; ?>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>css/fab_formularios_web_2.css">
	<!-- Select2 CSS -->
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>adm_assets/plugins/select2/dist/css/select2.min.css">
	<style>
        fieldset.is-invalid > legend {
            color: var(--bs-form-invalid-color, #dc3545);
        }

        fieldset.is-invalid .invalid-feedback.d-block {
            display: block !important;
        }

        .server-error-message ul {
            list-style-type: none;
            padding-left: 0;
        }

        .server-error-message li {
            margin-bottom: 0.5rem;
        }

        /* Select2 Bootstrap 5 styling */
        .select2-container--default .select2-selection--single {
            height: calc(2.25rem + 2px);
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: calc(2.25rem);
            padding-left: 0.75rem;
            color: #495057;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem);
            right: 0.75rem;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
        }

        /* Service Selection Styles - Exact copy from form_aliados_comerciales.view.php */
        .service-selection-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background-color: #f8f9fa;
        }

        /* Tab Container Width Alignment - Make tabs match content width */
        .service-category-tabs {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            width: 100%; /* Match the content area width */
        }

        .service-category-tabs .nav-link {
            background-color: #fff;
            border          : 1px solid #dee2e6;
            border-radius   : 8px;
            padding         : 12px 16px;
            text-align      : center;
            min-width       : 120px;
            transition      : all 0.3s ease;
            color           : #495057;
        }

        /* Service Category Tab Hover Text Color - Fix hover state */
        .service-category-tabs .nav-link:hover {
            background-color: #007bff;
            color           : #ffffff !important;
            border-color    : #007bff;
            transform       : translateY(-2px);
            box-shadow      : 0 4px 8px rgba(0,123,255,0.2);
        }

        .service-category-tabs .nav-link.active h6, .service-category-tabs .nav-link:hover h6 {
            color: white !important;
        }

        /* Active Tab Background Color - Use same blue as hover */
        .service-category-tabs .nav-link.active {
            background-color: #007bff !important;             /* Same blue as hover state */
            color           : #ffffff !important;             /* Ensure white text */
            border-color    : #007bff;
            transform       : translateY(-2px);
            box-shadow      : 0 4px 8px rgba(0,123,255,0.2);
        }

        .service-category-tabs .tab-icon {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .service-category-tabs h6 {
            font-size: 12px;
            font-weight: 600;
            margin: 0;
        }

        /* Remove Tab Pane Visual Contrast - Seamless integration */
        .service-category-content {
            background-color: transparent; /* Remove white background */
            border-radius: 0; /* Remove border radius */
            padding: 15px 0; /* Remove horizontal padding */
            min-height: 200px;
            border: none; /* Remove any borders */
            box-shadow: none; /* Remove any shadows */
        }

        .service-category-content .tab-pane {
            background-color: transparent; /* Remove tab pane background */
            border: none; /* Remove tab pane borders */
            box-shadow: none; /* Remove tab pane shadows */
            padding: 0; /* Remove tab pane padding */
        }

        /* Service Item Checkbox and Layout Improvements */
        .service-items .form-check {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
            cursor: pointer; /* Make entire container clickable */
            position: relative; /* For proper checkbox positioning */
        }

        .service-items .form-check:hover {
            background-color: #e9ecef;
            border-color: #007bff;
        }

        /* Active state with blue background and white text */
        .service-items .form-check.active {
            background-color: #007bff !important;
            border-color: #007bff !important;
            color: white !important;
        }

        .service-items .form-check.active .form-check-label {
            color: white !important;
        }

        /* Fix checkbox positioning within container - hide the actual checkbox */
        .service-items .form-check-input {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }

        .service-items .form-check-label {
            cursor: pointer;
            width: 100%;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 25px; /* Space for custom checkbox */
        }

        /* Custom checkbox indicator */
        .service-items .form-check-label::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #dee2e6;
            border-radius: 3px;
            background-color: white;
            transition: all 0.2s ease;
        }

        /* Custom checkbox checkmark */
        .service-items .form-check-label::after {
            content: '✓';
            position: absolute;
            left: 3px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #007bff;
            line-height: 1;
            opacity: 0;
            transition: all 0.2s ease;
        }

        /* Show checkmark when checked */
        .service-items .form-check.active .form-check-label::before {
            background-color: white;
            border-color: white;
        }

        .service-items .form-check.active .form-check-label::after {
            opacity: 1;
            color: #007bff;
        }

        /* Remove service priority number - Hide service-number */
        .service-items .form-check-label .service-number {
            display: none !important; /* Hide the priority numbers */
        }

        /* "Seleccionar todos" Button Styling Enhancement */
        .select-all-btn {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
            font-size: 13px;
            padding: 6px 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .select-all-btn:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
        }

        .select-all-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0,123,255,0.2);
        }

        /* Validation styles for service selection */
        fieldset.is-invalid {
            border-color: #dc3545;
        }

        fieldset.is-invalid .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }

        /* Enhanced Mobile responsive improvements */
        @media (max-width: 768px) {
            /* Improve service category tabs for mobile */
            .service-category-tabs {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
                margin-bottom: 16px;
            }

            .service-category-tabs .nav-link {
                min-width: auto;
                min-height: 44px; /* Minimum touch target */
                padding: 12px 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                flex-direction: row;
            }

            .service-category-tabs h6 {
                font-size: 14px; /* Improved readability */
                margin-left: 8px;
                margin-bottom: 0;
            }

            .service-category-tabs .tab-icon {
                font-size: 18px; /* Larger icons */
                margin-bottom: 0;
                margin-right: 0;
            }

            /* Single column layout for service items on mobile */
            .service-items .col-md-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }

            /* Enhanced service item containers */
            .service-items .form-check {
                padding: 15px;
                margin-bottom: 12px;
                min-height: 44px; /* Touch target compliance */
            }

            /* Larger custom checkbox for mobile */
            .service-items .form-check-label::before {
                width: 20px;
                height: 20px;
                border-width: 2px;
            }

            .service-items .form-check-label::after {
                left: 4px;
                font-size: 14px;
            }

            .service-items .form-check-label {
                padding-left: 30px;
                font-size: 14px;
                line-height: 1.4;
            }

            /* Improved select-all button */
            .select-all-btn {
                font-size: 14px;
                padding: 8px 16px;
                min-height: 44px;
                white-space: nowrap;
            }

            /* Better spacing for mobile header */
            .d-flex.justify-content-between.align-items-center.mb-3 {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .d-flex.justify-content-between.align-items-center.mb-3 h6 {
                text-align: center;
                margin-bottom: 0;
            }

            /* Enhanced container padding */
            .service-selection-container {
                padding: 16px;
            }

            /* Improved service category content */
            .service-category-content {
                padding: 16px 0;
            }
        }

        /* Additional breakpoint for very small screens */
        @media (max-width: 480px) {
            .service-category-tabs .nav-link {
                padding: 10px 12px;
                min-height: 40px;
				width: 100%;
            }

            .service-category-tabs h6 {
                font-size: 13px;
            }

            .service-items .form-check {
                padding: 12px;
            }

            .select-all-btn {
                font-size: 13px;
                padding: 6px 12px;
                min-height: 40px;
            }

            .service-selection-container {
                padding: 12px;
            }
        }

        /* Mobile/Desktop Responsive Classes - EXACT COPY FROM COMMERCIAL ALLIES */
        @media (max-width: 767px) {
            .desktop-only {
                display: none !important;
            }

            .mobile-only {
                display: block !important;
            }
        }

        @media (min-width: 768px) {
            .mobile-only {
                display: none !important;
            }

            .desktop-only {
                display: block !important;
            }
        }

        /* Mobile Accordion Styles - EXACT COPY FROM COMMERCIAL ALLIES */
        .service-categories-accordion {
            margin-bottom: 20px;
        }

        .accordion-item {
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
            background-color: #fff;
        }

        .accordion-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: #f8f9fa;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .accordion-header:hover {
            background-color: #e9ecef;
        }

        .accordion-header .tab-icon {
            font-size: 22px;
            color: #007bff;
            margin-right: 15px;
        }

        .accordion-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            flex-grow: 1;
        }

        .accordion-toggle {
            transition: transform 0.3s ease;
        }

        .accordion-header.active .accordion-toggle {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: #fff;
        }

        .accordion-content.active {
            max-height: 1000px;
            padding: 20px;
            border-top: 1px solid #dee2e6;
        }

        /* Mobile accordion service items */
        .accordion-content .service-items {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .accordion-content .form-check {
            margin-bottom: 8px;
            padding: 12px;
            padding-left: 40px; /* Make room for custom checkbox */
            border-radius: 6px;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
            position: relative; /* For absolute positioning of custom checkbox */
        }

        .accordion-content .form-check:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        /* Active state styling for mobile accordion service items */
        .accordion-content .form-check.active {
            background-color: #007bff !important;
            border-color: #007bff !important;
            color: white !important;
        }

        .accordion-content .form-check.active .form-check-label {
            color: white !important;
        }

        /* Mobile accordion checkbox styling - hide actual checkbox and show custom checkmark */
        .accordion-content .form-check-input {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }

        /* Custom checkmark for mobile accordion */
        .accordion-content .form-check-label::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 18px;
            border: 2px solid #dee2e6;
            border-radius: 3px;
            background-color: white;
            transition: all 0.2s ease;
        }

        .accordion-content .form-check-label::after {
            content: '✓';
            position: absolute;
            left: -21px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #007bff;
            opacity: 0;
            transition: all 0.2s ease;
        }

        /* Show checkmark when active */
        .accordion-content .form-check.active .form-check-label::before {
            background-color: white;
            border-color: white;
        }

        .accordion-content .form-check.active .form-check-label::after {
            opacity: 1;
            color: #007bff;
        }

        .btn-danger {
            padding: 0.3rem .5rem !important;
        }
	</style>
</head>

<body class="body-wrapper">
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
	<form action="form_freelancers" method="POST" id="freelancer-main-form" class="needs-validation" novalidate enctype="multipart/form-data">
		<div class="container mt-5" id="contenido_formulario">
			<h1 class="mb-4">Perfil de Freelancer</h1>
			<p class="text-muted">Completa tu perfil para colaborar con nosotros en proyectos emocionantes.</p>
			<p class="text-muted small mb-4"><span class="text-danger">*</span> Campo obligatorio</p>
			
			<?php // Mostrar mensajes de éxito o error del procesamiento POST (SOLO para recarga de página completa) ?>
			<?php if (!empty($form_success)): ?>
				<div class="alert alert-success alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($form_success, ENT_QUOTES, 'UTF-8'); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php elseif (!empty($form_errors)): ?>
				<div class="alert alert-danger alert-dismissible fade show server-error-message" role="alert">
					<strong>Error al procesar el formulario:</strong>
					<ul>
						<?php foreach ($form_errors as $key => $error): // Mostrar clave si es útil (ej. 'general') ?>
							<li><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></li>
						<?php endforeach; ?>
					</ul>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>
			
			<?php #region region NAVTAB header ?>
			<ul class="nav nav-tabs nav-fill" id="myTab" role="tablist">
				<li class="nav-item" role="presentation">
					<button class="nav-link active" id="personal-info-tab" data-bs-toggle="tab" data-bs-target="#personal-info" type="button" role="tab" aria-controls="personal-info" aria-selected="true">1. Información Personal</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="professional-experience-tab" data-bs-toggle="tab" data-bs-target="#professional-experience" type="button" role="tab" aria-controls="professional-experience" aria-selected="false">2. Experiencia Profesional</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="availability-tarifa-tab" data-bs-toggle="tab" data-bs-target="#availability-tarifa" type="button" role="tab" aria-controls="availability-tarifa" aria-selected="false">3. Trabajo</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="documentation-tab" data-bs-toggle="tab" data-bs-target="#documentation" type="button" role="tab" aria-controls="documentation" aria-selected="false">4. Documentación</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="consentimiento-tab" data-bs-toggle="tab" data-bs-target="#consentimiento" type="button" role="tab" aria-controls="consentimiento" aria-selected="false">5. Consentimiento</button>
				</li>
			</ul>
			<?php #endregion NAVTAB header ?>
			<div class="tab-content" id="myTabContent">
				<?php #region region TAB informacion personal ?>
				<div class="tab-pane fade show active" id="personal-info" role="tabpanel" aria-labelledby="personal-info-tab">
					<h5 class="mt-1 mb-3">Información Personal</h5>
					
					<div class="mb-3">
						<label for="nombre_completo" class="form-label">Nombre Completo: <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="nombre_completo" name="nombre_completo" required autocomplete="name">
						<div class="invalid-feedback">Por favor, ingresa tu nombre completo.</div>
					</div>
					<div class="row mb-3">
						<div class="col-md-4">
							<label for="tipo_documento" class="form-label">Tipo de Documento: <span class="text-danger">*</span></label>
							<select class="form-select" id="tipo_documento" name="tipo_documento" required>
								<option value="" selected disabled>-- Selecciona --</option>
								<option value="CC">C.C.</option>
								<option value="CE">C.E.</option>
								<option value="Pasaporte">Pasaporte</option>
							</select>
							<div class="invalid-feedback">Selecciona un tipo de documento.</div>
						</div>
						<div class="col-md-8">
							<label for="documento_identidad" class="form-label">Número de Documento: <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="documento_identidad" name="documento_identidad" placeholder="Ingrese el número sin puntos ni comas" required pattern="[0-9]+" inputmode="numeric">
							<div class="invalid-feedback">Ingresa un número de documento válido (no incluir letras o caracteres especiales).</div>
						</div>
					</div>
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="correo_electronico" class="form-label">Correo Electrónico: <span class="text-danger">*</span></label>
							<input type="email" class="form-control" id="correo_electronico" name="correo_electronico" placeholder="<EMAIL>" required autocomplete="email">
							<div class="invalid-feedback">Ingresa un correo electrónico válido.</div>
						</div>
						<div class="col-md-6">
							<label for="numero_telefono" class="form-label">Número de Teléfono: <span class="text-danger">*</span></label>
							<input type="tel" class="form-control" id="numero_telefono" name="numero_telefono" placeholder="Ej: +57 ************" required pattern="^\+?[0-9\s\-()]{7,}$" autocomplete="tel">
							<div class="invalid-feedback">Ingresa un número de teléfono válido.</div>
						</div>
					</div>
					<div class="mb-3">
						<label for="direccion_completa" class="form-label">Dirección Completa: <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="direccion_completa" name="direccion_completa" placeholder="Calle, número, barrio, etc." required autocomplete="address-line1">
						<div class="invalid-feedback">Ingresa tu dirección completa.</div>
					</div>
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="pais_residencia" class="form-label">País de Residencia: <span class="text-danger">*</span></label>
							<select class="form-select" id="pais_residencia" name="pais_residencia" required>
								<option value="">Seleccionar país...</option>
								<?php if (isset($paises) && is_array($paises)): ?>
									<?php foreach ($paises as $pais): ?>
										<option value="<?php echo htmlspecialchars($pais, ENT_QUOTES, 'UTF-8'); ?>"
											<?php echo (isset($form_data['pais_residencia']) && $form_data['pais_residencia'] === $pais) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($pais, ENT_QUOTES, 'UTF-8'); ?>
										</option>
									<?php endforeach; ?>
								<?php endif; ?>
							</select>
							<div class="invalid-feedback">Selecciona tu país de residencia.</div>
						</div>
						<div class="col-md-6">
							<label for="ciudad_residencia" class="form-label">Ciudad de Residencia: <span class="text-danger">*</span></label>
							<select class="form-select" id="ciudad_residencia" name="ciudad_residencia" required
								<?php echo empty($form_data['pais_residencia']) ? 'disabled' : ''; ?>>
								<option value="">
									<?php echo empty($form_data['pais_residencia']) ? 'Primero selecciona un país...' : 'Seleccionar ciudad...'; ?>
								</option>
							</select>
							<div class="invalid-feedback">Selecciona tu ciudad de residencia.</div>
						</div>
					</div>

					<div class="d-flex justify-content-end mt-4">
						<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
					</div>
				</div>
				<?php #endregion TAB informacion personal ?>
				<?php #region region TAB experiencia profesional ?>
				<div class="tab-pane fade" id="professional-experience" role="tabpanel" aria-labelledby="professional-experience-tab">
					<h5 class="mt-1 mb-3">Experiencia Profesional</h5>
					
					<div class="mb-3">
						<label for="resumen_profesional" class="form-label">Resumen Profesional: <span class="text-danger">*</span></label>
						<textarea class="form-control" id="resumen_profesional" name="resumen_profesional" rows="4" placeholder="Describe brevemente tu experiencia, habilidades clave y áreas de especialización." required maxlength="1000"></textarea> <?php // Increased maxlength example ?>
						<div class="invalid-feedback">Por favor, ingresa tu resumen profesional.</div>
					</div>
					<div class="mb-3">
						<label for="certificaciones_relevantes" class="form-label">Certificaciones Relevantes: <span class="text-danger">*</span></label>
						<textarea class="form-control" id="certificaciones_relevantes" name="certificaciones_relevantes" rows="3" placeholder="Lista certificaciones importantes (ej: Scrum Master, AWS Certified Developer, Google Ads Certification). Sepáralas por comas o saltos de línea." required></textarea>
						<div class="invalid-feedback">Indica tus certificaciones o escribe "Ninguna".</div>
					</div>

					<?php #region region Portafolio de servicios ?>
					<div class="mb-4">
						<h5 class="mt-3 mb-3">Portafolio de servicios <span class="text-danger">*</span></h5>
						<p class="text-muted mb-3">Seleccione los servicios que puede ofrecer como freelancer. Debe seleccionar al menos un servicio.</p>

						<!-- Desktop Service Selection -->
						<fieldset data-group-required="true" class="service-selection-container desktop-only">
							<?php if (!empty($categorias_servicios)): ?>
								<!-- Service Category Tabs -->
								<ul class="service-category-tabs nav nav-pills mb-3" id="serviceTabs" role="tablist">
									<?php $n_categoria = 0; ?>
									<?php foreach ($categorias_servicios as $categoria_servicio): ?>
										<li class="nav-item category-tab" role="presentation">
											<button class="nav-link <?php echo ($n_categoria == 0) ? 'active' : ''; ?>"
													id="categoria-<?php echo $categoria_servicio->getId(); ?>-tab"
													data-bs-toggle="pill"
													data-bs-target="#categoria-<?php echo $categoria_servicio->getId(); ?>"
													type="button"
													role="tab"
													aria-controls="categoria-<?php echo $categoria_servicio->getId(); ?>"
													aria-selected="<?php echo ($n_categoria == 0) ? 'true' : 'false'; ?>">
												<div class="tab-icon"><i class="<?php echo $categoria_servicio->getIcono(); ?>"></i></div>
												<h6 class="mb-0"><?php echo $categoria_servicio->getDescripcion(); ?></h6>
											</button>
										</li>
										<?php $n_categoria++; ?>
									<?php endforeach; ?>
								</ul>

								<!-- Service Category Content -->
								<div class="tab-content service-category-content" id="serviceTabContent">
									<?php $n_categoria = 0; ?>
									<?php foreach ($categorias_servicios as $categoria_servicio): ?>
										<div class="tab-pane fade <?php echo ($n_categoria == 0) ? 'show active' : ''; ?>"
											 id="categoria-<?php echo $categoria_servicio->getId(); ?>"
											 role="tabpanel"
											 aria-labelledby="categoria-<?php echo $categoria_servicio->getId(); ?>-tab">

											<div class="d-flex justify-content-between align-items-center mb-3 mobile-service-header">
												<h6 class="mb-0 service-category-title"><?php echo $categoria_servicio->getDescripcion(); ?></h6>
												<button type="button" class="btn btn-sm btn-primary select-all-btn"
														data-category="<?php echo $categoria_servicio->getId(); ?>"
														aria-label="Seleccionar todos los servicios de <?php echo htmlspecialchars($categoria_servicio->getDescripcion(), ENT_QUOTES, 'UTF-8'); ?>">
													<span class="select-btn-text">Seleccionar todos</span>
												</button>
											</div>

											<div class="service-items row">
												<?php foreach ($categoria_servicio->getServicios() as $servicio): ?>
													<div class="col-md-6 mb-2">
														<div class="form-check service-item"
															 onclick="toggleServiceCheckbox('servicio_<?php echo $servicio->getId(); ?>')"
															 role="button"
															 tabindex="0"
															 aria-label="Seleccionar servicio: <?php echo htmlspecialchars($servicio->getDescripcion(), ENT_QUOTES, 'UTF-8'); ?>">
															<input class="form-check-input"
																   type="checkbox"
																   value="<?php echo $servicio->getId(); ?>"
																   id="servicio_<?php echo $servicio->getId(); ?>"
																   name="servicios[]"
																   data-category="<?php echo $categoria_servicio->getId(); ?>"
																   aria-describedby="servicio_<?php echo $servicio->getId(); ?>_desc">
															<label class="form-check-label" for="servicio_<?php echo $servicio->getId(); ?>" id="servicio_<?php echo $servicio->getId(); ?>_desc">
																<span class="service-number"><?php echo $servicio->getPrioridad(); ?></span>
																<?php echo $servicio->getDescripcion(); ?>
															</label>
														</div>
													</div>
												<?php endforeach; ?>
											</div>
										</div>
										<?php $n_categoria++; ?>
									<?php endforeach; ?>
								</div>
							<?php else: ?>
								<div class="alert alert-warning">
									<i class="bi bi-exclamation-triangle"></i>
									No hay servicios disponibles en este momento.
								</div>
							<?php endif; ?>

							<div class="invalid-feedback">Debe seleccionar al menos un servicio.</div>
						</fieldset>

						<!-- Mobile Accordion View (hidden on desktop) - EXACT COPY FROM COMMERCIAL ALLIES -->
						<fieldset data-group-required="true" class="service-selection-container mobile-only">
							<?php if (!empty($categorias_servicios)): ?>
								<div class="service-categories-accordion">
									<?php foreach ($categorias_servicios as $categoria_servicio): ?>
										<div class="accordion-item" data-category="mobile_categoria_<?php echo $categoria_servicio->getId(); ?>">
											<!-- Accordion Header -->
											<div class="accordion-header">
												<div class="tab-icon"><i class="<?php echo $categoria_servicio->getIcono(); ?>"></i></div>
												<h4><?php echo $categoria_servicio->getDescripcion(); ?></h4>
												<div class="accordion-toggle">
													<i class="fas fa-chevron-down"></i>
												</div>
											</div>

											<!-- Accordion Content -->
											<div class="accordion-content">
												<div class="d-flex justify-content-between align-items-center mb-3">
													<h6 class="mb-0"><?php echo $categoria_servicio->getDescripcion(); ?></h6>
													<button type="button" class="btn btn-sm btn-primary select-all-btn"
															data-category="<?php echo $categoria_servicio->getId(); ?>"
															aria-label="Seleccionar todos los servicios de <?php echo htmlspecialchars($categoria_servicio->getDescripcion(), ENT_QUOTES, 'UTF-8'); ?>">
														<span class="select-btn-text">Seleccionar todos</span>
													</button>
												</div>

												<div class="service-items">
													<?php foreach ($categoria_servicio->getServicios() as $servicio): ?>
														<div class="form-check service-item"
															 onclick="toggleServiceCheckbox('servicio_<?php echo $servicio->getId(); ?>')"
															 role="button"
															 tabindex="0"
															 aria-label="Seleccionar servicio: <?php echo htmlspecialchars($servicio->getDescripcion(), ENT_QUOTES, 'UTF-8'); ?>">
															<input class="form-check-input"
																   type="checkbox"
																   value="<?php echo $servicio->getId(); ?>"
																   id="servicio_<?php echo $servicio->getId(); ?>"
																   name="servicios[]"
																   data-category="<?php echo $categoria_servicio->getId(); ?>"
																   aria-describedby="servicio_<?php echo $servicio->getId(); ?>_desc">
															<label class="form-check-label" for="servicio_<?php echo $servicio->getId(); ?>" id="servicio_<?php echo $servicio->getId(); ?>_desc">
																<span class="service-number"><?php echo $servicio->getPrioridad(); ?></span>
																<?php echo $servicio->getDescripcion(); ?>
															</label>
														</div>
													<?php endforeach; ?>
												</div>
											</div>
										</div>
									<?php endforeach; ?>
								</div>
							<?php else: ?>
								<div class="alert alert-warning">
									<i class="bi bi-exclamation-triangle"></i>
									No hay servicios disponibles en este momento.
								</div>
							<?php endif; ?>
							<div class="invalid-feedback">Debe seleccionar al menos un servicio.</div>
						</fieldset>
					</div>
					<?php #endregion Portafolio de servicios ?>

					<?php #region region Idiomas ?>
					<div class="mb-4">
						<h5 class="mt-3 mb-3">Idiomas</h5>
						<p class="text-muted mb-3">Agregue los idiomas que domina y su nivel de competencia.</p>

						<!-- Language Input Form -->
						<div class="row mb-3">
							<div class="col-md-6 mb-2">
								<label for="idioma_select" class="form-label">Idioma</label>
								<select class="form-select" id="idioma_select">
									<option value="">Seleccionar idioma...</option>
									<?php if (isset($idiomas) && is_array($idiomas)): ?>
										<?php foreach ($idiomas as $idioma): ?>
											<option value="<?php echo htmlspecialchars((string)$idioma->getId(), ENT_QUOTES, 'UTF-8'); ?>">
												<?php echo htmlspecialchars($idioma->getNombre(), ENT_QUOTES, 'UTF-8'); ?>
											</option>
										<?php endforeach; ?>
									<?php endif; ?>
								</select>
								<div id="idioma-error" class="text-danger small mt-1" style="display: none;"></div>
							</div>
							<div class="col-md-4 mb-2">
								<label for="nivel_idioma" class="form-label">Nivel</label>
								<select class="form-select" id="nivel_idioma">
									<option value="">Seleccionar nivel</option>
									<option value="Básico">Básico</option>
									<option value="Intermedio">Intermedio</option>
									<option value="Avanzado">Avanzado</option>
									<option value="Nativo">Nativo</option>
								</select>
							</div>
							<div class="col-md-2 mb-2">
								<label class="form-label">&nbsp;</label>
								<button type="button" class="btn btn-primary d-block w-100" id="agregar-idioma-btn">
									<i class="bi bi-plus-circle"></i> Agregar
								</button>
							</div>
						</div>

						<!-- Languages List -->
						<div id="idiomas-list-container">
							<div id="idiomas-empty-message" class="alert alert-info">
								<i class="bi bi-info-circle"></i>
								No se han agregado idiomas aún. Use el formulario anterior para agregar idiomas.
							</div>
							<div id="idiomas-table-container" style="display: none;">
								<div class="table-responsive">
									<table class="table table-striped table-hover">
										<thead style="background-color: var(--azul-oscuro-empresarial); color: white;">
											<tr>
												<th>Idioma</th>
												<th>Nivel</th>
												<th class="text-center" width="100">Acciones</th>
											</tr>
										</thead>
										<tbody id="idiomas-table-body">
											<!-- Languages will be added here dynamically -->
										</tbody>
									</table>
								</div>
							</div>
						</div>

						<!-- Hidden inputs to store language data for form submission -->
						<div id="idiomas-hidden-inputs">
							<!-- Hidden inputs will be added here dynamically -->
						</div>
					</div>
					<?php #endregion Idiomas ?>

					<div class="d-flex justify-content-between mt-4">
						<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
						<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
					</div>
				</div>
				<?php #endregion TAB experiencia profesional ?>
				<?php #region region TAB trabajo disponibilidad ?>
				<div class="tab-pane fade" id="availability-tarifa" role="tabpanel" aria-labelledby="availability-tab">
					<h5 class="mt-1 mb-3">Disponibilidad y Tarifas</h5>
					
					<div class="row">
						<div class="col-md-6 mb-3">
							<?php // Agrupado con fieldset para validación JS ?>
							<fieldset data-group-required="true" data-group-name="disponibilidad_tiempo">
								<legend class="form-label fs-6">Disponibilidad de Tiempo: <span class="text-danger">*</span></legend>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="disponibilidad_tiempo_completo" name="disponibilidad_tiempo[]" value="completo">
									<label class="form-check-label" for="disponibilidad_tiempo_completo">Tiempo Completo (aprox. 40h/semana)</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="disponibilidad_medio_tiempo" name="disponibilidad_tiempo[]" value="medio">
									<label class="form-check-label" for="disponibilidad_medio_tiempo">Medio Tiempo (aprox. 20h/semana)</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="disponibilidad_por_horas" name="disponibilidad_tiempo[]" value="horas">
									<label class="form-check-label" for="disponibilidad_por_horas">Por Horas (según proyecto)</label>
								</div>
								<div class="invalid-feedback d-block">Selecciona al menos una opción de disponibilidad.</div>
							</fieldset>
						</div>
						<div class="col-md-6 mb-3">
							<?php // Agrupado con fieldset para validación JS ?>
							<fieldset data-group-required="true" data-group-name="modalidad_colaboracion">
								<legend class="form-label fs-6">Modalidad preferida de colaboración: <span class="text-danger">*</span></legend>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="modalidad_proyecto" name="modalidad_colaboracion[]" value="proyecto">
									<label class="form-check-label" for="modalidad_proyecto">Por proyecto (Freelance)</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="modalidad_contrato_largo_plazo" name="modalidad_colaboracion[]" value="largo_plazo">
									<label class="form-check-label" for="modalidad_contrato_largo_plazo">Contrato a largo plazo (Remoto)</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="modalidad_hibrido" name="modalidad_colaboracion[]" value="hibrido">
									<label class="form-check-label" for="modalidad_hibrido">Híbrido (Remoto/Presencial)</label>
								</div>
								<div class="invalid-feedback d-block">Selecciona al menos una modalidad de colaboración.</div>
							</fieldset>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6 mb-3">
							<label for="id_huso_horario" class="form-label">Zona Horaria: <span class="text-danger">*</span></label>
							<select class="form-select" id="id_huso_horario" name="id_huso_horario" required>
								<option value="" selected disabled>-- Selecciona --</option>
								<?php if (isset($husos_horarios) && is_array($husos_horarios)): ?>
									<?php foreach ($husos_horarios as $huso_horario): ?>
										<option value="<?php echo htmlspecialchars((string)$huso_horario->getId(), ENT_QUOTES, 'UTF-8'); ?>">
											<?php echo htmlspecialchars($huso_horario->getHusoHorario() . ' (' . $huso_horario->getPaises() . ')', ENT_QUOTES, 'UTF-8'); // Example display ?>
										</option>
									<?php endforeach; ?>
								<?php endif; ?>
							</select>
							<small id="huso_horario_info" class="form-text text-muted">Seleccione UTC -05:00 si vive en Colombia.</small> <?php // For JS timezone info ?>
							<div class="invalid-feedback">Selecciona tu zona horaria.</div>
						</div>
						<div class="col-md-6 mb-3">
							<label for="tarifa_por_hora" class="form-label">Tarifa por Hora:</label>
							<div class="input-group">
								<input type="text" class="form-control" id="tarifa_por_hora" placeholder="Ej: 40.000" inputmode="numeric">
								<span class="input-group-text">COP</span>
								<input type="hidden" id="tarifa_por_hora_raw" name="tarifa_por_hora">
							</div>
							<small class="form-text text-muted">Indica tu tarifa horaria deseada o estimada.</small>
							<div class="invalid-feedback">Ingresa un valor numérico válido si deseas indicar tarifa.</div>
						</div>

					</div>
					<div class="mb-3">
						<label for="proyectos_realizados" class="form-label">Proyectos Relevantes Realizados:</label>
						<textarea class="form-control" id="proyectos_realizados" name="proyectos_realizados" rows="4" placeholder="Describe brevemente proyectos clave, indicando tu rol y los resultados. Puedes incluir enlaces si son públicos."></textarea>
					</div>
					<fieldset class="mb-3 border p-3 rounded">
						<legend class="form-label fs-6 w-auto px-2">Referencias Laborales (Mínimo 1)</legend>
						<small class="form-text text-muted d-block mb-3">
							Proporciona el contacto de personas que puedan dar fe de tu trabajo (jefes anteriores, clientes).
						</small>
						<div class="mb-3">
							<label class="form-label small fw-bold">Referencia 1 <span class="text-danger">*</span></label>
							<div class="row g-2">
								<div class="col-md-6">
									<label for="nombre_completo_ref1" class="form-label visually-hidden">Nombre Completo Ref. 1:</label>
									<input type="text" class="form-control form-control-sm" id="nombre_completo_ref1" name="nombre_completo_ref1" placeholder="Nombre Completo" required>
									<div class="invalid-feedback">Nombre de referencia 1 requerido.</div>
								</div>
								<div class="col-md-6">
									<label for="numero_telefono_ref1" class="form-label visually-hidden">Teléfono Ref. 1:</label>
									<input type="text" class="form-control form-control-sm" id="numero_telefono_ref1" name="numero_telefono_ref1" placeholder="Teléfono" required>
									<div class="invalid-feedback">Contacto de referencia 1 requerido.</div>
								</div>
							</div>
						</div>
						<div class="mb-3">
							<label class="form-label small fw-bold">Referencia 2</label>
							<div class="row g-2">
								<div class="col-md-6">
									<label for="nombre_completo_ref2" class="form-label visually-hidden">Nombre Completo Ref. 2:</label>
									<input type="text" class="form-control form-control-sm" id="nombre_completo_ref2" name="nombre_completo_ref2" placeholder="Nombre Completo">
								</div>
								<div class="col-md-6">
									<label for="numero_telefono_ref2" class="form-label visually-hidden">Teléfono Ref. 2:</label>
									<input type="text" class="form-control form-control-sm" id="numero_telefono_ref2" name="numero_telefono_ref2" placeholder="Teléfono">
								</div>
							</div>
						</div>
					</fieldset>
					<div class="d-flex justify-content-between mt-4">
						<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
						<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
					</div>
				</div>
				<?php #endregion TAB trabajo disponibilidad ?>
				<?php #region region TAB documentacion ?>
				<div class="tab-pane fade" id="documentation" role="tabpanel" aria-labelledby="documentation-tab">
					<h5 class="mt-1 mb-3">Documentación</h5>
					
					<div class="mb-4">
						<label for="cv_file" class="form-label">Currículum Vitae (CV): <span class="text-danger">*</span></label>
						<input type="file" class="form-control" id="cv_file" name="cv_file" accept=".pdf,.doc,.docx" required>
						<small class="form-text text-muted">Adjunta tu CV actualizado. Formatos permitidos: PDF (peso máx. 5MB).</small>
						<?php // Bootstrap 5 añade icono de validación automáticamente al input .is-invalid ?>
						<div class="invalid-feedback">Por favor adjunta tu CV PDF.</div>
					</div>
					<div class="mb-4">
						<label for="certificaciones_file" class="form-label">Certificaciones y Diplomas:</label>
						<input type="file" class="form-control" id="certificaciones_file" name="certificaciones_file" accept=".pdf">
						<small class="form-text text-muted">Adjunta certificaciones o diplomas relevantes en un solo archivo PDF (peso máx. 5MB).</small>
						<?php // Bootstrap 5 añade icono de validación automáticamente al input .is-invalid ?>
						<div class="invalid-feedback">Por favor, sube solo archivos PDF.</div>
					</div>
					<div class="d-flex justify-content-between mt-4">
						<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
						<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
					</div>
				</div>
				<?php #endregion TAB documentacion ?>
				<?php #region region TAB consentimiento ?>
				<div class="tab-pane fade" id="consentimiento" role="tabpanel" aria-labelledby="consentimiento-tab">
					<h5 class="mt-1 mb-3">Consentimiento y Verificación <span class="text-danger">*</span></h5>
					<p class="text-muted">Por favor, lee y acepta los siguientes puntos para completar tu perfil.</p>
					
					<div class="form-check mb-3">
						<input class="form-check-input" type="checkbox" value="1" id="veracidad_informacion" name="veracidad_informacion" required>
						<label class="form-check-label" for="veracidad_informacion">
							Declaro que toda la información proporcionada en este formulario es veraz y completa.
						</label>
						<div class="invalid-feedback">Debes aceptar esta declaración.</div>
					</div>
					
					<div class="form-check mb-3">
						<input class="form-check-input" type="checkbox" value="1" id="consentimiento_datos" name="consentimiento_datos" required>
						<label class="form-check-label" for="consentimiento_datos">
							Autorizo el tratamiento de mis datos personales conforme a la <a href="/politica-privacidad" target="_blank" rel="noopener noreferrer">Política de Privacidad</a> de la plataforma. <?php // Actualiza el enlace si es necesario ?>
						</label>
						<div class="invalid-feedback">Debes autorizar el tratamiento de datos.</div>
					</div>
					
					<div class="form-check mb-4">
						<input class="form-check-input" type="checkbox" value="1" id="acuerdo_terminos" name="acuerdo_terminos" required>
						<label class="form-check-label" for="acuerdo_terminos">
							He leído y acepto los <a href="politica_consentimiento" target="_blank" rel="noopener noreferrer">Términos y Condiciones de la política de declaración y consentimiento</a>. <?php // Actualiza el enlace si es necesario ?>
						</label>
						<div class="invalid-feedback">Debes aceptar los términos y condiciones.</div>
					</div>
					
					<div class="alert alert-info small d-flex align-items-center" role="alert">
						<i class="bi bi-info-circle-fill me-2"></i>
						<div>Tu perfil será revisado. Podríamos contactarte si necesitamos alguna aclaración.</div>
					</div>
					
					<?php #region region botones navegacion --- boton enviar perfil submit formulario ?>
					<div class="d-flex justify-content-between mt-4">
						<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
						<button type="submit" class="btn btn-success btn-lg" id="submit-button"><i class="bi bi-check-circle-fill"></i> Enviar Perfil</button>
					</div>
					<?php #endregion botones navegacion --- boton enviar perfil submit formulario ?>
				</div>
				<?php #endregion TAB consentimiento ?>
			</div>
		</div>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/footer_section.view.php'; ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_section.view.php'; ?>
<!-- Select2 JS -->
<script src="<?php echo RUTA_RESOURCES ?>adm_assets/plugins/select2/dist/js/select2.min.js"></script>

<script>
    // Function to toggle service checkbox when clicking on the container
    function toggleServiceCheckbox(checkboxId) {
        // Find ALL checkboxes with this ID (desktop and mobile versions have same ID)
        const allCheckboxes = document.querySelectorAll(`input[id="${checkboxId}"]`);

        if (allCheckboxes.length > 0) {
            // Toggle the state for all versions
            const newState = !allCheckboxes[0].checked;

            allCheckboxes.forEach(checkbox => {
                checkbox.checked = newState;
                // Update visual state for each checkbox
                updateServiceItemVisualState(checkbox);
            });

            // Trigger change event on the first checkbox to update "Select All" button text
            allCheckboxes[0].dispatchEvent(new Event('change'));
        }
    }

    // Function to update visual state of service item
    function updateServiceItemVisualState(checkbox) {
        const container = checkbox.closest('.form-check');
        if (container) {
            if (checkbox.checked) {
                container.classList.add('active');
            } else {
                container.classList.remove('active');
            }
        }
    }

    document.addEventListener('DOMContentLoaded', () => {
        
        const mainForm            = document.getElementById('freelancer-main-form'); // ID del formulario principal
        const tabButtonContainer  = document.getElementById('myTab');
        const tabContentContainer = document.getElementById('myTabContent');
        const submitButton        = document.getElementById('submit-button');
        
        // Initialize Bootstrap Tabs if Bootstrap JS is loaded
        if (typeof bootstrap !== 'undefined' && tabButtonContainer) {
            const triggerTabList = Array.from(tabButtonContainer.querySelectorAll('button[data-bs-toggle="tab"]'));
            triggerTabList.forEach(triggerEl => {
                new bootstrap.Tab(triggerEl);
            });
        } else {
            console.error('Bootstrap JS not loaded or tab container not found, tabs might not function correctly.');
        }

        // --- Service Selection Functionality ---
        // Handle "Select All" buttons
        document.querySelectorAll('.select-all-btn').forEach(button => {
            button.addEventListener('click', (event) => {
                event.stopPropagation(); // Prevent the click from bubbling up to accordion header

                const categoryId = button.getAttribute('data-category');
                const checkboxes = document.querySelectorAll(`input[type="checkbox"][data-category="${categoryId}"]`);
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                // Toggle all checkboxes in this category
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !allChecked;
                    // Update visual state for each checkbox
                    updateServiceItemVisualState(checkbox);
                });

                // Update button text (handle both class names for compatibility)
                const buttonText = button.querySelector('.select-btn-text') || button.querySelector('.select-all-btn-text') || button;
                buttonText.textContent = allChecked ? 'Seleccionar todos' : 'Deseleccionar todos';
            });
        });

        // Update "Select All" button text when individual checkboxes change
        document.querySelectorAll('input[type="checkbox"][name="servicios[]"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                // Update visual state
                updateServiceItemVisualState(checkbox);

                const categoryId = checkbox.getAttribute('data-category');
                const categoryCheckboxes = document.querySelectorAll(`input[type="checkbox"][data-category="${categoryId}"]`);
                const selectAllBtn = document.querySelector(`.select-all-btn[data-category="${categoryId}"]`);

                if (selectAllBtn) {
                    const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);
                    const buttonText = selectAllBtn.querySelector('.select-btn-text') || selectAllBtn.querySelector('.select-all-btn-text') || selectAllBtn;
                    buttonText.textContent = allChecked ? 'Deseleccionar todos' : 'Seleccionar todos';
                }
            });
        });

        // Initialize visual states on page load
        document.querySelectorAll('input[type="checkbox"][name="servicios[]"]').forEach(checkbox => {
            updateServiceItemVisualState(checkbox);
        });

        // Add keyboard support for service items
        document.querySelectorAll('.service-item[role="button"]').forEach(item => {
            item.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault();
                    const checkbox = item.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        toggleServiceCheckbox(checkbox.id);
                    }
                }
            });

            // Add click event listener to prevent event bubbling to accordion header
            item.addEventListener('click', (event) => {
                event.stopPropagation(); // Prevent the click from bubbling up to accordion header
            });
        });

        // Mobile accordion functionality - EXACT COPY FROM COMMERCIAL ALLIES
        const accordionHeaders = document.querySelectorAll('.accordion-header');

        if (accordionHeaders.length > 0) {
            accordionHeaders.forEach(header => {
                header.addEventListener('click', (event) => {
                    // Only toggle accordion if the click is directly on the header, not on child elements
                    if (event.target === header || header.contains(event.target)) {
                        // Toggle active class on header
                        header.classList.toggle('active');

                        // Toggle content visibility
                        const content = header.nextElementSibling;
                        if (content && content.classList.contains('accordion-content')) {
                            content.classList.toggle('active');
                        }
                    }
                });
            });

            // Open first accordion by default
            if (accordionHeaders[0]) {
                accordionHeaders[0].classList.add('active');
                const firstContent = accordionHeaders[0].nextElementSibling;
                if (firstContent) {
                    firstContent.classList.add('active');
                }
            }
        }

        // Handle window resize to ensure proper checkbox state sync between views
        window.addEventListener('resize', () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][name="servicios[]"]');
            const checkboxesById = {};

            // Create a map of checkboxes by ID
            checkboxes.forEach(checkbox => {
                const id = checkbox.id;
                if (!checkboxesById[id]) {
                    checkboxesById[id] = [];
                }
                checkboxesById[id].push(checkbox);
            });

            // Sync checkbox states
            for (const id in checkboxesById) {
                if (checkboxesById[id].length > 1) {
                    const isChecked = checkboxesById[id][0].checked;
                    checkboxesById[id].forEach(checkbox => {
                        checkbox.checked = isChecked;
                        updateServiceItemVisualState(checkbox);
                    });
                }
            }
        });
        
        // Function to switch tabs programmatically using Bootstrap API
        const switchTab = (targetTabButtonId) => { // Expects button ID like 'personal-info-tab'
            const targetTabButton = document.getElementById(targetTabButtonId);
            if (targetTabButton && typeof bootstrap !== 'undefined') {
                const tabInstance = bootstrap.Tab.getInstance(targetTabButton) || new bootstrap.Tab(targetTabButton);
                tabInstance.show();
                // Scroll to top of form container after tab switch
                document.getElementById('contenido_formulario')?.scrollIntoView({behavior: 'smooth', block: 'start'});
            } else {
                if (targetTabButtonId) console.error(`Tab button with ID ${targetTabButtonId} not found or Bootstrap JS missing.`);
            }
        };
        
        // Function to validate the form section within a given pane element
        const validatePane = (paneElement) => {
            if (!paneElement) return false;
            
            // Use the main form for validation context, but only consider elements within the pane
            let isPaneValid = true;
            
            // Reset validation state visually for elements within this pane first
            paneElement.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            paneElement.querySelectorAll('.was-validated').forEach(el => el.classList.remove('was-validated')); // Might need more specific targeting
            paneElement.querySelectorAll('.invalid-feedback.d-block').forEach(el => el.classList.remove('d-block'));
            paneElement.querySelectorAll('fieldset.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            
            
            // Standard required fields within the pane
            const elementsToValidate = paneElement.querySelectorAll('input[required], select[required], textarea[required]');
            elementsToValidate.forEach(el => {
                if (!el.checkValidity()) {
                    isPaneValid = false;
                    el.classList.add('is-invalid');
                } else {
                    el.classList.add('is-valid'); // Add valid class for feedback
                }
            });
            
            // Radio groups within the pane
            const radioGroups = {};
            paneElement.querySelectorAll('input[type="radio"][required]').forEach(radio => {
                const name = radio.name;
                if (radioGroups[name] === undefined) radioGroups[name] = false;
                if (radio.checked) radioGroups[name] = true;
            });
            for (const name in radioGroups) {
                if (!radioGroups[name]) {
                    isPaneValid             = false;
                    const firstRadioInGroup = paneElement.querySelector(`input[type="radio"][name="${name}"][required]`);
                    firstRadioInGroup?.classList.add('is-invalid');
                    firstRadioInGroup?.closest('.form-check')?.classList.add('is-invalid'); // Mark container too
                } else {
                    // Optionally mark as valid?
                }
            }
            
            // Checkbox groups marked with data-group-required="true" within the pane
            const checkboxGroups = paneElement.querySelectorAll('fieldset[data-group-required="true"]');
            checkboxGroups.forEach(group => {
                const checkboxes   = group.querySelectorAll('input[type="checkbox"]');
                const isGroupValid = Array.from(checkboxes).some(cb => cb.checked);
                if (!isGroupValid) {
                    isPaneValid = false;
                    group.classList.add('is-invalid'); // Style fieldset
                    group.querySelector('.invalid-feedback')?.classList.add('d-block'); // Show message
                } else {
                    group.classList.remove('is-invalid');
                    group.querySelector('.invalid-feedback')?.classList.remove('d-block');
                }
            });
            
            // Apply 'was-validated' to the PARENT FORM to show feedback messages correctly for BS5
            // but only if validation failed within this specific pane check
            if (!isPaneValid) {
                mainForm.classList.add('was-validated'); // Apply to main form
                console.warn(`Validation failed in tab: ${paneElement.id}`);
                const firstInvalid = paneElement.querySelector('.is-invalid, :invalid, fieldset.is-invalid');
                firstInvalid?.scrollIntoView({behavior: 'smooth', block: 'center'});
                firstInvalid?.focus();
            } else {
                // If pane is valid, we might still want to keep was-validated on the main form
                // if other panes have been validated. Or remove it only from this pane's elements?
                // For simplicity, let's just add was-validated on failure for now.
                // To show green ticks, add was-validated even on success:
                // mainForm.classList.add('was-validated');
            }
            
            return isPaneValid;
        };
        
        // --- Navigation Button Listeners (Validation ALWAYS runs now) ---
		<?php #region region JS botones de navegacion ?>
        document.querySelectorAll('.siguiente-tab, .siguiente_info_personal, .siguiente_exp_profesional, .siguiente_disponibilidad, .siguiente_documentacion').forEach(button => {
            button.addEventListener('click', () => {
                const currentPane = button.closest('.tab-pane');
                if (validatePane(currentPane)) { // Validate current pane first
                    const nextPane = currentPane?.nextElementSibling;
                    if (nextPane && nextPane.classList.contains('tab-pane')) {
                        switchTab(nextPane.id + '-tab'); // Switch using the button ID convention
                    }
                }
            });
        });
        
        document.querySelectorAll('.anterior-tab, .anterior_exp_profesional, .anterior_disponibilidad, .anterior_documentacion, .anterior_consentimiento').forEach(button => {
            button.addEventListener('click', () => {
                const currentPane = button.closest('.tab-pane');
                const prevPane    = currentPane?.previousElementSibling;
                if (prevPane && prevPane.classList.contains('tab-pane')) {
                    switchTab(prevPane.id + '-tab'); // Switch using the button ID convention
                }
            });
        });
		<?php #endregion JS botones de navegacion ?>
        // --- End Navigation Button Listeners ---
        
        
        // --- Final Form Submission Logic ---
		<?php #region region JS -- AJAX form submission ?>
        if (mainForm && submitButton) {
            mainForm.addEventListener('submit', async (event) => {
                event.preventDefault(); // Prevent default browser submission ALWAYS first
                event.stopPropagation(); // Stop propagation as well
                
                let allTabsValid = true;
                const allPanes   = mainForm.querySelectorAll('.tab-pane');
                
                // Validate all panes sequentially before submitting
                for (const pane of allPanes) {
                    if (!validatePane(pane)) { // Validate each pane
                        allTabsValid           = false;
                        const errorTabButtonId = pane.id + '-tab';
                        switchTab(errorTabButtonId); // Switch to the tab with the error
                        showSweetAlertError('Error de validación',`Por favor, corrige los errores indicados en la pestaña "${document.getElementById(errorTabButtonId)?.textContent || pane.id}".`);
                        break; // Stop checking other tabs
                    }
                }
                
                // If any tab failed validation, stop submission
                if (!allTabsValid) {
                    mainForm.classList.add('was-validated'); // Ensure feedback styles are shown globally
                    return;
                }
                
                // --- If all tabs valid, proceed with submission ---
                mainForm.classList.add('was-validated'); // Ensure all fields show valid state if needed
                
                const formData = new FormData(mainForm);
                formData.append('is_ajax', '1'); // <-- Marcar como petición AJAX
                
                // --- Optional: Display collected data for debugging ---
                // console.log("--- FormData a enviar ---");
                // for (let [key, value] of formData.entries()) {
                //     console.log(`${key}:`, value instanceof File ? value.name : value);
                // }
                // return; // Descomentar para detener antes de enviar y ver datos
                
                // --- Actual Submission ---
                submitButton.disabled  = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Enviando...';
                
                try {
                    // Use the form's action attribute for the endpoint
                    const response = await fetch(mainForm.action, {
                        method: 'POST',
                        body  : formData
                        // No es necesario 'Content-Type': 'multipart/form-data' aquí, FormData lo maneja
                    });
                    
                    // --- MANEJO DE RESPUESTA JSON ---
                    const result = await response.json(); // Esperar respuesta JSON
                    
                    if (response.ok && result.success) { // Verificar status HTTP y flag 'success' en JSON
                        // Show success message and redirect after user acknowledges
                        swal({
                            title: '¡Éxito!',
                            text: result.message || '¡Perfil enviado con éxito!',
                            icon: 'success',
                            button: {
                                text: "OK",
                                value: true,
                                visible: true,
                                className: "btn-success",
                                closeModal: true
                            }
                        }).then((value) => {
                            if (value) {
                                // Redirect to index.view.php after user clicks OK
                                window.location.href = 'index';
                            }
                        });

                        mainForm.reset();
                        mainForm.classList.remove('was-validated');
                        allPanes.forEach(p => {
                            p.classList.remove('was-validated');
                            p.querySelectorAll('.is-invalid, .is-valid').forEach(el => el.classList.remove('is-invalid', 'is-valid'));
                            p.querySelectorAll('.invalid-feedback.d-block').forEach(el => el.classList.remove('d-block'));
                            p.querySelectorAll('fieldset.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                        });
                        switchTab('personal-info-tab');
                    } else {
                        // Error HTTP o success=false en JSON
                        console.error('Submission failed:', response.status, result);
                        // Mostrar mensaje de error del servidor o uno genérico
                        let errorMessage = `Error: ${result.message || response.statusText}`;
                        if (result.errors) {
                            // Opcional: mostrar errores específicos de campos si vienen en result.errors
                            errorMessage += "\nDetalles: " + JSON.stringify(result.errors);
                        }
                        showSweetAlertError('Error al enviar',`No se pudo guardar el perfil.<br><small>${errorMessage.replace(/\n/g, '<br>')}</small>`);
                    }
                    // --- FIN MANEJO DE RESPUESTA JSON ---
                    
                } catch (error) {
                    // Error de red o al parsear JSON
                    console.error('Error during submission or parsing:', error);
                    showSweetAlertError('Error de comunicación','Error de comunicación o respuesta inválida del servidor. Por favor, inténtalo de nuevo.');
                } finally {
                    // Re-enable button regardless of success or failure
                    submitButton.disabled  = false;
                    submitButton.innerHTML = '<i class="bi bi-check-circle-fill"></i> Enviar Perfil';
                }
            });
        } else {
            console.error("Could not find form '#freelancer-main-form' or button '#submit-button'");
        }

        // Currency formatting for 'tarifa_por_hora'
        const tarifaPorHoraInput = document.getElementById('tarifa_por_hora');
        const tarifaPorHoraRawInput = document.getElementById('tarifa_por_hora_raw');

        if (tarifaPorHoraInput && tarifaPorHoraRawInput) {
            // Initial format on page load if there's a value
            if (tarifaPorHoraInput.value) {
                const initialValue = parseFloat(tarifaPorHoraInput.value.replace(/\./g, '').replace(',', '.'));
                if (!isNaN(initialValue)) {
                    tarifaPorHoraRawInput.value = initialValue;
                        tarifaPorHoraInput.value = new Intl.NumberFormat('es-CO', {
                            style: 'currency',
                            currency: 'COP',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                        }).format(initialValue);
                    }
                }

                tarifaPorHoraInput.addEventListener('input', (event) => {
                    let value = event.target.value;

                    // Remove all non-digit characters except for comma
                    let cleanValue = value.replace(/[^0-9,]/g, '');

                    // Replace comma with dot for internal float conversion
                    let numericValue = cleanValue.replace(',', '.');

                    // Convert to integer (no decimals)
                    let intValue = parseInt(numericValue);

                    if (isNaN(intValue)) {
                        tarifaPorHoraRawInput.value = '';
                        event.target.value = '';
                        return;
                    }

                    // Update the raw hidden input with the numeric value
                    tarifaPorHoraRawInput.value = intValue;

                    // Format for display using Intl.NumberFormat without decimals
                    event.target.value = new Intl.NumberFormat('es-CO', {
                        style: 'currency',
                        currency: 'COP',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }).format(intValue);
                });

                // On blur, re-format to ensure consistent display and remove currency symbol
                tarifaPorHoraInput.addEventListener('blur', (event) => {
                    const rawValue = tarifaPorHoraRawInput.value;
                    if (rawValue !== '') {
                        const intValue = parseInt(rawValue);
                        // Format without currency symbol, but with grouping for display
                        event.target.value = new Intl.NumberFormat('es-CO', {
                            style: 'decimal',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                        }).format(intValue);
                    }
                });
                
                // On focus, convert back to raw number for easier editing
                tarifaPorHoraInput.addEventListener('focus', (event) => {
                    const rawValue = tarifaPorHoraRawInput.value;
                    if (rawValue !== '') {
                        event.target.value = rawValue;
                    }
                });
            }

        // --- Language Management Functionality ---
        let idiomasData = []; // Array to store language data temporarily

        // Initialize language autocomplete
        const idiomaSelect = document.getElementById('idioma_select');
        const idiomaError = document.getElementById('idioma-error');
        const nivelSelect = document.getElementById('nivel_idioma');
        const agregarBtn = document.getElementById('agregar-idioma-btn');

        // Initialize Select2 for idioma dropdown with search functionality
        if (idiomaSelect) {
            $(idiomaSelect).select2({
                placeholder: 'Seleccionar idioma...',
                allowClear: true,
                width: '100%',
                language: {
                    noResults: function() {
                        return "No se encontraron resultados";
                    },
                    searching: function() {
                        return "Buscando...";
                    }
                }
            });
        }

        // Add language button functionality
        if (agregarBtn) {
            agregarBtn.addEventListener('click', function() {
                const idiomaId = $(idiomaSelect).val();
                const idiomaNombre = $(idiomaSelect).find('option:selected').text();
                const nivel = nivelSelect.value;

                // Validation
                if (!idiomaId || !idiomaNombre || idiomaId === '') {
                    showError('Por favor, seleccione un idioma válido de la lista.');
                    return;
                }

                if (!nivel) {
                    showError('Por favor, seleccione un nivel de competencia.');
                    return;
                }

                // Check for duplicates
                const isDuplicate = idiomasData.some(item => item.id === parseInt(idiomaId));
                if (isDuplicate) {
                    showError('Este idioma ya ha sido agregado.');
                    return;
                }

                // Add to data array
                idiomasData.push({
                    id: parseInt(idiomaId),
                    nombre: idiomaNombre,
                    nivel: nivel
                });

                // Update UI
                updateIdiomasDisplay();
                updateHiddenInputs();

                // Clear form
                $(idiomaSelect).val('').trigger('change');
                nivelSelect.value = '';
                idiomaError.style.display = 'none';
            });
        }

        // Function to show error messages
        function showError(message) {
            idiomaError.textContent = message;
            idiomaError.style.display = 'block';
        }

        // Function to update the languages display
        function updateIdiomasDisplay() {
            const emptyMessage = document.getElementById('idiomas-empty-message');
            const tableContainer = document.getElementById('idiomas-table-container');
            const tableBody = document.getElementById('idiomas-table-body');

            if (idiomasData.length === 0) {
                emptyMessage.style.display = 'block';
                tableContainer.style.display = 'none';
            } else {
                emptyMessage.style.display = 'none';
                tableContainer.style.display = 'block';

                // Clear existing rows
                tableBody.innerHTML = '';

                // Add rows for each language
                idiomasData.forEach((idioma, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${idioma.nombre}</td>
                        <td>${idioma.nivel}</td>
                        <td class="text-center">
                            <button type="button" class="btn btn-sm btn-danger" onclick="removeIdioma(${index})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        }

        // Function to remove a language
        window.removeIdioma = function(index) {
            // Show confirmation dialog
            swal({
                title: '¿Eliminar idioma?',
                text: `¿Está seguro de que desea eliminar "${idiomasData[index].nombre}" de la lista?`,
                icon: 'warning',
                buttons: {
                    cancel: {
                        text: "Cancelar",
                        value: null,
                        visible: true,
                        className: "btn-secondary",
                        closeModal: true
                    },
                    confirm: {
                        text: "Eliminar",
                        value: true,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                }
            }).then((willDelete) => {
                if (willDelete) {
                    // Remove from array
                    idiomasData.splice(index, 1);

                    // Update UI
                    updateIdiomasDisplay();
                    updateHiddenInputs();
                }
            });
        };

        // Function to update hidden inputs for form submission
        function updateHiddenInputs() {
            const container = document.getElementById('idiomas-hidden-inputs');
            container.innerHTML = '';

            idiomasData.forEach((idioma, index) => {
                // Create hidden inputs for each language
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = `idiomas[${index}][id_idioma]`;
                idInput.value = idioma.id;

                const nivelInput = document.createElement('input');
                nivelInput.type = 'hidden';
                nivelInput.name = `idiomas[${index}][nivel]`;
                nivelInput.value = idioma.nivel;

                container.appendChild(idInput);
                container.appendChild(nivelInput);
            });
        }

        // Initialize Select2 for País dropdown with search functionality
        const paisSelect = document.getElementById('pais_residencia');
        const ciudadSelect = document.getElementById('ciudad_residencia');

        if (paisSelect) {
            $(paisSelect).select2({
                placeholder: 'Seleccionar país...',
                allowClear: true,
                width: '100%',
                language: {
                    noResults: function() {
                        return "No se encontraron resultados";
                    },
                    searching: function() {
                        return "Buscando...";
                    }
                }
            });
        }

        // Initialize Select2 for Ciudad dropdown with search functionality
        if (ciudadSelect) {
            $(ciudadSelect).select2({
                placeholder: 'Primero selecciona un país...',
                allowClear: true,
                width: '100%',
                disabled: true,
                language: {
                    noResults: function() {
                        return "No se encontraron resultados";
                    },
                    searching: function() {
                        return "Buscando...";
                    }
                }
            });
        }

        // Cascading Country-City Dropdown Functionality
        if (paisSelect && ciudadSelect) {
            // Function to load cities for a given country
            function loadCities(selectedPais, selectedCiudad = '') {
                if (!selectedPais) {
                    // Clear and disable city dropdown
                    $(ciudadSelect).empty().append('<option value="">Primero selecciona un país...</option>');
                    $(ciudadSelect).prop('disabled', true).trigger('change');
                    return;
                }

                // Show loading state
                $(ciudadSelect).empty().append('<option value="">Cargando ciudades...</option>');
                $(ciudadSelect).prop('disabled', true).trigger('change');

                fetch(`form_freelancers?action=get_cities&pais=${encodeURIComponent(selectedPais)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.ciudades) {
                            // Clear loading message and add cities
                            $(ciudadSelect).empty().append('<option value="">Seleccionar ciudad...</option>');

                            data.ciudades.forEach(ciudad => {
                                const option = new Option(ciudad, ciudad, false, ciudad === selectedCiudad);
                                $(ciudadSelect).append(option);
                            });

                            // Enable city dropdown and refresh Select2
                            $(ciudadSelect).prop('disabled', false).trigger('change');
                        } else {
                            $(ciudadSelect).empty().append('<option value="">Error al cargar ciudades</option>');
                            console.error('Error loading cities:', data.message || 'Unknown error');
                        }
                    })
                    .catch(error => {
                        $(ciudadSelect).empty().append('<option value="">Error al cargar ciudades</option>');
                        console.error('Error fetching cities:', error);
                    });
            }

            // Handle country selection change using Select2 event
            $(paisSelect).on('change', function() {
                const selectedPais = $(this).val();
                loadCities(selectedPais);
            });

            // Load cities on page load if country is already selected (form validation errors)
            <?php if (!empty($form_data['pais_residencia'])): ?>
                // Set initial values and load cities
                $(paisSelect).val('<?php echo addslashes($form_data['pais_residencia']); ?>').trigger('change');
                setTimeout(function() {
                    loadCities('<?php echo addslashes($form_data['pais_residencia']); ?>', '<?php echo addslashes($form_data['ciudad_residencia'] ?? ''); ?>');
                }, 100);
            <?php endif; ?>
        }

		<?php #endregion JS form submission ?>
    });
</script>
<?php #endregion JS ?>

</body>

</html>
