<?php

use App\classes\AliadoComercial;
use App\classes\ClientePotencial; // Added
use App\classes\ServicioCategoria;
use App\classes\Servicio;
use App\classes\PaisCiudad;

session_start();

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

require_once __ROOT__ . '/vendor/autoload.php';

/**
 * Verify Google reCAPTCHA v2 response
 * @param string $recaptchaResponse The reCAPTCHA response token
 * @return bool True if verification successful, false otherwise
 */
function verifyRecaptcha($recaptchaResponse) {
    if (empty($recaptchaResponse)) {
        return false;
    }

    $secretKey = RECAPTCHA_SECRET_KEY;
    $verifyUrl = RECAPTCHA_VERIFY_URL;

    $postData = [
        'secret' => $secretKey,
        'response' => $recaptchaResponse,
        'remoteip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($postData),
            'timeout' => 10
        ]
    ];

    $context = stream_context_create($options);
    $result = @file_get_contents($verifyUrl, false, $context);

    if ($result === false) {
        error_log('reCAPTCHA verification failed: Unable to connect to Google servers');
        return false;
    }

    $resultJson = json_decode($result, true);

    if (!$resultJson) {
        error_log('reCAPTCHA verification failed: Invalid JSON response');
        return false;
    }

    if (!isset($resultJson['success']) || !$resultJson['success']) {
        $errorCodes = isset($resultJson['error-codes']) ? implode(', ', $resultJson['error-codes']) : 'Unknown error';
        error_log('reCAPTCHA verification failed: ' . $errorCodes);
        return false;
    }

    return true;
}

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
#endregion init variables

#region region GET (AJAX Handling)
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		// Handle AJAX request for cities by country
		if (isset($_GET['action']) && $_GET['action'] === 'get_cities' && isset($_GET['pais'])) {
			$pais = trim($_GET['pais']);
			if (!empty($pais)) {
				$ciudades = PaisCiudad::get_ciudades_por_pais($pais, $conexion);
				header('Content-Type: application/json');
				echo json_encode(['success' => true, 'ciudades' => $ciudades]);
			} else {
				header('Content-Type: application/json');
				echo json_encode(['success' => false, 'message' => 'País requerido']);
			}
			exit;
		}

	} catch (Exception $e) {
		if (isset($_GET['action']) && $_GET['action'] === 'get_cities') {
			header('Content-Type: application/json');
			echo json_encode(['success' => false, 'message' => $e->getMessage()]);
			exit;
		}
		error_log("Error in GET section: " . $e->getMessage());
	}
}
#endregion GET (AJAX Handling)

#region region POST (AJAX Handling)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];
	
	try {
		// Set the default timezone for date functions
		date_default_timezone_set('America/Bogota');

		// Start transaction
		$conexion->beginTransaction();
		error_log("Transaction started in form_aliados_comerciales.php");

		// --- reCAPTCHA Verification ---
		$recaptchaResponse = trim($_POST['g-recaptcha-response'] ?? '');
		if (!verifyRecaptcha($recaptchaResponse)) {
			throw new Exception('Verificación reCAPTCHA fallida. Por favor, completa la verificación de seguridad.');
		}
		error_log("reCAPTCHA verification successful");

		// --- Data Retrieval and Sanitization (Aliado) ---
		$nombre_razon_social = trim($_POST['nombre_razon_social'] ?? '');
		$cedula_nit          = trim($_POST['cedula_nit'] ?? '');
		$telefono_contacto   = trim($_POST['telefono_contacto'] ?? '');
		$correo_electronico  = trim($_POST['correo_electronico'] ?? '');
		$ciudad_operacion    = trim($_POST['ciudad_operacion'] ?? '');
		$pais_operacion      = trim($_POST['pais_operacion'] ?? '');
		$tipo_alianza        = trim($_POST['tipo_alianza'] ?? '');
		// Retrieve checkbox values (1 if checked/value is '1', 0 otherwise)
		$acepta_declaracion_veracidad = isset($_POST['declaracion_veracidad']) && $_POST['declaracion_veracidad'] === '1' ? 1 : 0;
		$acepta_terminos_condiciones  = isset($_POST['aceptacion_terminos']) && $_POST['aceptacion_terminos'] === '1' ? 1 : 0;
		
		// --- Object Creation and Population ---
		$aliado = new AliadoComercial();
		$aliado->setNombreRazonSocial($nombre_razon_social)
			->setCedulaNit($cedula_nit)
			->setTelefonoContacto($telefono_contacto)
			->setCorreoElectronico($correo_electronico)
			->setCiudadOperacion($ciudad_operacion)
			->setPaisOperacion($pais_operacion)
			->setTipoAlianza($tipo_alianza)
			->setAceptaDeclaracionVeracidad($acepta_declaracion_veracidad) // Set acceptance status
			->setAceptaTerminosCondiciones($acepta_terminos_condiciones); // Set acceptance status
		
		// --- Save Aliado to Database ---
		// The guardar() method now includes validation for these fields
		if (!$aliado->guardar($conexion)) {
			// Throw exception if guardar returns false (though it usually throws on error)
			throw new Exception("No se pudo guardar el registro del aliado.");
		}
		$aliadoId = $aliado->getId(); // Get the ID of the newly saved aliado
		
		// --- Data Retrieval and Sanitization (Cliente Potencial) ---
		$nombre_empresa_cliente    = trim($_POST['nombre_empresa_cliente'] ?? '');
		$nombre_contacto_cliente   = trim($_POST['nombre_contacto_cliente'] ?? '');
		$cargo_contacto_cliente    = trim($_POST['cargo_contacto_cliente'] ?? '');
		$telefono_contacto_cliente = trim($_POST['telefono_contacto_cliente'] ?? '');
		$correo_contacto_cliente   = trim($_POST['correo_contacto_cliente'] ?? '');
		$ciudad_cliente            = trim($_POST['ciudad_cliente'] ?? '');
		$pais_cliente              = trim($_POST['pais_cliente'] ?? '');
		// --- Data Retrieval (Oportunidad Tab) ---
		$descripcion_negocio = trim($_POST['descripcion_negocio'] ?? '');

		// --- Data Retrieval (Services Selection) ---
		$servicios_seleccionados = [];
		if (isset($_POST['servicios']) && is_array($_POST['servicios'])) {
			$servicios_raw = $_POST['servicios'];
			error_log("Raw services received: " . json_encode($servicios_raw));

			$servicios_seleccionados = array_map('intval', $servicios_raw);
			$servicios_seleccionados = array_filter($servicios_seleccionados, function($id) {
				return $id > 0;
			});

			$servicios_before_dedup = $servicios_seleccionados;
			// Remove duplicates that can occur when both desktop and mobile checkboxes are submitted
			$servicios_seleccionados = array_unique($servicios_seleccionados);
			// Re-index the array to ensure clean sequential keys
			$servicios_seleccionados = array_values($servicios_seleccionados);

			error_log("Services before deduplication: " . json_encode($servicios_before_dedup));
			error_log("Services after deduplication: " . json_encode($servicios_seleccionados));
		}

		// --- Create and Populate Cliente Potencial ---
		$clientePotencial = new ClientePotencial();
		$clientePotencial->setAliadoComercialId($aliadoId) // Link to the Aliado
			->setNombreEmpresa($nombre_empresa_cliente)
			->setNombreContacto($nombre_contacto_cliente)
			->setCargoContacto($cargo_contacto_cliente ?: null) // Optional field
			->setTelefonoContacto($telefono_contacto_cliente)
			->setCorreoContacto($correo_contacto_cliente)
			->setCiudad($ciudad_cliente)
			->setPais($pais_cliente)
			->setDescripcionNegocio($descripcion_negocio ?: null); // Set description
		
		// --- Save Cliente Potencial to Database (this will also save specializations) ---
		if (!$clientePotencial->guardar($conexion)) {
			// Throw exception if guardar returns false
			throw new Exception("No se pudo guardar el registro del cliente potencial.");
		}

		// --- Save Selected Services ---
		if (!empty($servicios_seleccionados)) {
			if (!$clientePotencial->guardarServicios($conexion, $servicios_seleccionados)) {
				throw new Exception("No se pudieron guardar los servicios seleccionados.");
			}
		} else {
			throw new Exception("Debe seleccionar al menos un servicio.");
		}
		
		// --- Commit Transaction ---
		$conexion->commit();
		error_log("Transaction committed in form_aliados_comerciales.php");

		// --- Prepare Email Parameters for Background Processing ---
		$emailParams = [
			'nombre_aliado'   => $nombre_razon_social,   // Corresponds to 'nombre_razon_social' from form
			'correo_aliado'   => $correo_electronico,    // Corresponds to 'correo_electronico' from form
			'tipo_alianza'    => $tipo_alianza,          // Corresponds to 'tipo_alianza' from form
			'telefono_aliado' => $telefono_contacto      // Add phone for management notification
		];

		// --- Send Emails ---
		// Using synchronous email sending for reliability and performance
		$emailsSent = false;

		try {
			// Send welcome email
			PHPMailerCorreo::enviar_correo_aliado_comercial($emailParams);
			error_log("Correo de bienvenida enviado exitosamente a: " . $correo_electronico);

			// Send management notification
			try {
				PHPMailerCorreo::enviar_correo_gerencia_aliados_comerciales($emailParams, $conexion);
				error_log("Correo de notificación a gerencia enviado exitosamente para el aliado: " . $nombre_razon_social);
			} catch (Exception $mgmtEmailEx) {
				error_log("Error enviando correo de notificación a gerencia: " . $mgmtEmailEx->getMessage());
			}

			$emailsSent = true;

		} catch (Exception $emailEx) {
			// Log email error but don't stop the success response for data saving
			error_log("Error en envío de correos para " . $correo_electronico . ": " . $emailEx->getMessage());
			// Note: We could add a flag to the response indicating email issues if needed
		}

		// Log final email status
		if ($emailsSent) {
			error_log("Correos enviados exitosamente para aliado: " . $nombre_razon_social);
		} else {
			error_log("ADVERTENCIA: No se pudieron enviar correos para aliado: " . $nombre_razon_social);
		}
		
		$response['success'] = true;
		$response['message'] = '¡Registro de aliado y cliente potencial enviado con éxito!';
		// Optionally add IDs: $response['aliado_id'] = $aliadoId; $response['cliente_id'] = $clientePotencial->getId();
		
	} catch (PDOException $e) {
		$conexion->rollBack(); // Rollback transaction on DB error
		error_log("Transaction rolled back in form_aliados_comerciales.php");
		error_log("PDOException in form_aliados_comerciales.php: " . $e->getMessage());
		$response['message'] = 'Error de base de datos al guardar el registro.';
		// Specific check for unique constraint violation (adjust code if needed)
		if ($e->getCode() == 23000 || str_contains($e->getMessage(), 'Duplicate entry')) {
			$response['message'] = 'Error: La Cédula/NIT o el Correo Electrónico ya existen.';
			http_response_code(409); // Conflict
		} else {
			http_response_code(500); // Internal Server Error
		}
	} catch (Exception $e) {
		$conexion->rollBack(); // Rollback transaction on general error
		error_log("Exception in form_aliados_comerciales.php: " . $e->getMessage());
		$response['message'] = 'Error al procesar el registro: ' . $e->getMessage();
		// Check if it's a validation error from validarDatos()
		if (str_contains($e->getMessage(), 'requerido') || str_contains($e->getMessage(), 'válido')) {
			http_response_code(400); // Bad Request for validation errors
		} else {
			http_response_code(500); // Internal Server Error for other exceptions
		}
	}
	
	echo json_encode($response);
	exit; // Stop script execution for AJAX requests
}
/**
 * Sends emails in background to avoid blocking the user response.
 * This function uses a simple approach that works on most hosting environments.
 *
 * @param string $tipo Type of email: 'aliado_comercial' or 'gerencia_aliados'
 * @param array $emailParams Email parameters
 * @throws Exception If background process cannot be started
 */
function enviarCorreoEnBackground(string $tipo, array $emailParams): void
{
	// For immediate improvement, we'll use a simple approach that works on most servers
	// In production, consider using a proper queue system like Redis/RabbitMQ

	// Check if background processor exists
	$processorFile = __ROOT__ . "/src/background/procesar_correos.php";
	if (!file_exists($processorFile)) {
		throw new Exception("Background email processor not found: {$processorFile}");
	}

	// Check if temp directory is writable
	$tempDir = sys_get_temp_dir();
	if (!is_writable($tempDir)) {
		throw new Exception("Temp directory is not writable: {$tempDir}");
	}

	// Serialize email data for background processing
	$emailData = [
		'tipo' => $tipo,
		'params' => $emailParams,
		'timestamp' => time()
	];

	// Create a temporary file with email data
	$tempFile = $tempDir . '/lunex_email_' . uniqid() . '.json';
	$jsonData = json_encode($emailData);

	if (file_put_contents($tempFile, $jsonData) === false) {
		throw new Exception("Failed to create temp file: {$tempFile}");
	}

	// Verify file was created
	if (!file_exists($tempFile)) {
		throw new Exception("Temp file was not created: {$tempFile}");
	}

	// Execute background email processor (non-blocking)
	$command = "php " . escapeshellarg($processorFile) . " " . escapeshellarg($tempFile) . " > /dev/null 2>&1 &";

	if (PHP_OS_FAMILY === 'Windows') {
		// Windows command
		$command = "start /B php " . escapeshellarg($processorFile) . " " . escapeshellarg($tempFile) . " > NUL 2>&1";
	}

	error_log("Executing background email command: {$command}");
	exec($command, $output, $returnCode);

	// Note: $returnCode will always be 0 for background processes, so we can't rely on it
	error_log("Background email process started for type: {$tipo}, temp file: {$tempFile}");
}

#endregion POST (AJAX Handling)

#region region GET (Page Load) / Non-AJAX POST (Fallback - currently not used by JS)
// Load service categories and services data for the form
try {
	// Obtener todas las categorías de servicios activas ordenadas por prioridad
	$categorias_servicios = ServicioCategoria::get_list($conexion);

	// Load countries data for dropdowns
	$paises = PaisCiudad::get_paises($conexion);

	// Optimized: Get all services in a single query instead of N+1 queries
	$todos_servicios = Servicio::get_list($conexion);

	// Group services by category for efficient assignment
	$servicios_por_categoria = [];
	foreach ($todos_servicios as $servicio) {
		$id_categoria = $servicio->getId_servicio_categoria();
		if (!isset($servicios_por_categoria[$id_categoria])) {
			$servicios_por_categoria[$id_categoria] = [];
		}
		$servicios_por_categoria[$id_categoria][] = $servicio;
	}

	// Assign services to their respective categories
	foreach ($categorias_servicios as $categoria) {
		$id_categoria = $categoria->getId();
		$categoria->setServicios($servicios_por_categoria[$id_categoria] ?? []);
	}

} catch (Exception $e) {
	error_log("Error loading service data for form: " . $e->getMessage());
	$categorias_servicios = [];
}

// Include the view only for GET requests or non-AJAX POSTs
require_once __ROOT__ . '/views/form_aliados_comerciales.view.php';

?>
